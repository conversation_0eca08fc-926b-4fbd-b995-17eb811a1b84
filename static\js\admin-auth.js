/**
 * 管理员认证工具 - 简洁版本
 * 专门处理JWT token的存储和API请求增强
 * 
 * 设计原则：
 * 1. 最小化修改现有代码
 * 2. 自动拦截和增强API请求
 * 3. 透明的token管理
 * 
 * 作者: AI Assistant
 * 创建时间: 2025-01-08
 */

(function(window) {
    'use strict';

    // 管理员认证管理器
    const AdminAuth = {
        // 存储键名
        STORAGE_KEYS: {
            ACCESS_TOKEN: 'admin_access_token',
            REFRESH_TOKEN: 'admin_refresh_token',
            ADMIN_ID: 'admin_id',
            ADMIN_ROLE: 'admin_role'
        },

        // 初始化
        init: function() {
            this.interceptFetch();
            this.interceptXHR();

            // 页面加载时检查认证状态
            setTimeout(() => {
                this.checkAuthOnPageLoad();
            }, 100);

        },

        // 存储token信息
        storeTokens: function(accessToken, refreshToken, adminId, adminRole) {
            try {
                localStorage.setItem(this.STORAGE_KEYS.ACCESS_TOKEN, accessToken);
                localStorage.setItem(this.STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
                localStorage.setItem(this.STORAGE_KEYS.ADMIN_ID, adminId);
                localStorage.setItem(this.STORAGE_KEYS.ADMIN_ROLE, adminRole);

                // 解析并显示token调试信息
                this.logTokenDebugInfo(accessToken, 'Access Token');
                if (refreshToken) {
                    this.logTokenDebugInfo(refreshToken, 'Refresh Token');
                }

                // 存储浏览器缓存信息 -> 登录时后端返回的Token信息
                return true;
            } catch (e) {
                return false;
            }
        },

        // 获取访问token
        getAccessToken: function() {
            return localStorage.getItem(this.STORAGE_KEYS.ACCESS_TOKEN);
        },

        // 获取刷新token
        getRefreshToken: function() {
            return localStorage.getItem(this.STORAGE_KEYS.REFRESH_TOKEN);
        },

        // 获取管理员ID
        getAdminId: function() {
            return localStorage.getItem(this.STORAGE_KEYS.ADMIN_ID);
        },

        // 获取管理员角色
        getAdminRole: function() {
            return localStorage.getItem(this.STORAGE_KEYS.ADMIN_ROLE);
        },

        // 清除所有认证信息
        clearAuth: function() {
            Object.values(this.STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
        },

        // 检查是否已登录
        isAuthenticated: function() {
            return !!this.getAccessToken();
        },

        // 设置刚刚登录成功的标志
        setJustLoggedIn: function(value) {
            this.justLoggedIn = value;
            if (value) {
                // 3秒后清除标志
                setTimeout(() => {
                    this.justLoggedIn = false;
                }, 3000);
            }
        },

        // 检查是否刚刚登录成功
        isJustLoggedIn: function() {
            return !!this.justLoggedIn;
        },

        // 显示认证提示模态框
        showAuthModal: function(title, message, buttonText = '重新登录') {
            // 标记Token过期状态，用于优先级控制
            localStorage.setItem('tokenExpired', 'true');

            // 查找模态框元素
            const modal = document.getElementById('authModal');
            const titleElement = document.getElementById('authModalTitle');
            const messageElement = document.getElementById('authModalMessage');
            const buttonElement = document.getElementById('authModalButton');

            if (modal && titleElement && messageElement && buttonElement) {
                // 设置模态框内容
                titleElement.textContent = title;
                messageElement.textContent = message;
                buttonElement.textContent = buttonText;

                // 显示模态框
                modal.classList.add('show');

                // 绑定按钮点击事件
                buttonElement.onclick = () => {
                    window.location.href = '/admin/backend/logon.html';
                };
            } else {
                // 如果模态框不存在，回退到alert
                if (typeof alert !== 'undefined') {
                    alert(message);
                    setTimeout(() => {
                        window.location.href = '/admin/backend/logon.html';
                    }, 100);
                }
            }
        },

        // 处理认证失败，跳转到登录页面
        handleAuthFailure: function(reason = '认证失败') {
            // 如果刚刚登录成功，跳过认证失败处理
            if (this.isJustLoggedIn()) {
                return;
            }

            this.clearAuth();

            // 避免在登录页面重复跳转
            if (!window.location.pathname.includes('/admin/login') &&
                !window.location.pathname.includes('/logon.html') &&
                !window.location.pathname.includes('/admin/backend/logon.html')) {

                // 使用模态框显示Token过期提示
                this.showAuthModal(
                    'Token已过期',
                    '您的登录凭证已过期，请重新登录以继续操作。',
                    '重新登录'
                );
            }
        },

        // 检查API响应是否表示认证失败
        isAuthFailureResponse: function(response) {
            // 检查HTTP状态码
            if (response.status === 401 || response.status === 403) {
                return true;
            }

            // 检查响应头中的认证错误标识
            const authError = response.headers.get('X-Auth-Error');
            if (authError) {
                return true;
            }

            return false;
        },

        // 检查响应内容是否表示认证失败
        checkResponseForAuthFailure: function(responseData) {
            if (typeof responseData === 'object' && responseData !== null) {
                // 检查常见的认证失败响应格式
                if (responseData.error &&
                    (responseData.error.includes('token') ||
                     responseData.error.includes('认证') ||
                     responseData.error.includes('登录') ||
                     responseData.error.includes('权限'))) {
                    return true;
                }

                if (responseData.code &&
                    (responseData.code === 401 || responseData.code === 403)) {
                    return true;
                }

                if (responseData.message &&
                    (responseData.message.includes('token') ||
                     responseData.message.includes('认证') ||
                     responseData.message.includes('登录'))) {
                    return true;
                }

                // 检查msg字段（Django后端常用）
                if (responseData.msg &&
                    (responseData.msg.includes('token') ||
                     responseData.msg.includes('认证') ||
                     responseData.msg.includes('登录') ||
                     responseData.msg.includes('令牌'))) {
                    return true;
                }

                // 检查特定的错误代码
                if (responseData.error_code &&
                    (responseData.error_code === 'NO_ADMIN_TOKEN' ||
                     responseData.error_code === 'INVALID_TOKEN' ||
                     responseData.error_code === 'TOKEN_EXPIRED')) {
                    return true;
                }
            }
            return false;
        },

        // 检查是否为本站API请求
        isLocalApiRequest: function(url) {
            if (typeof url !== 'string') return false;

            // 检查是否包含/api/路径
            if (!url.includes('/api/')) return false;

            // 如果是完整URL，检查是否为本站域名
            if (url.startsWith('http://') || url.startsWith('https://')) {
                try {
                    const urlObj = new URL(url);
                    const currentHost = window.location.host;
                    // 只对本站的API请求添加认证头
                    return urlObj.host === currentHost ||
                           urlObj.host === 'localhost:8000' ||
                           urlObj.host === '127.0.0.1:8000';
                } catch (e) {
                    return false;
                }
            }

            // 相对路径的API请求视为本站请求
            return true;
        },

        // 检查是否为登录相关API
        isLoginApiRequest: function(url) {
            if (typeof url !== 'string') return false;
            return url.includes('/loginAdmin') ||
                   url.includes('/administrator_login') ||
                   url.includes('/login');
        },

        // 拦截fetch请求，自动添加认证头
        interceptFetch: function() {
            const originalFetch = window.fetch;
            const self = this;

            window.fetch = function(url, options = {}) {
                // 只对本站API请求添加认证头，但排除登录API
                if (self.isLocalApiRequest(url) && !self.isLoginApiRequest(url)) {
                    const accessToken = self.getAccessToken();

                    if (accessToken) {
                        options.headers = options.headers || {};
                        options.headers['X-Admin-Access-Token'] = accessToken;
                        options.headers['X-Admin-ID'] = self.getAdminId();
                        options.headers['X-Admin-Role'] = self.getAdminRole();
                    } else {
                        // 如果没有token，记录日志但不阻止请求

                    }
                } else if (self.isLoginApiRequest(url)) {
                    // 登录API请求，不添加认证头
                }

                // 调用原始fetch
                return originalFetch.call(this, url, options).then(response => {
                    // 只对本站API请求检查认证失败，排除登录API
                    if (self.isLocalApiRequest(url) && !self.isLoginApiRequest(url)) {


                        if (self.isAuthFailureResponse(response)) {

                            self.handleAuthFailure('服务器返回认证失败');
                            return response; // 仍然返回响应，让调用者处理
                        }

                        // 对于200状态码，还需要检查响应体内容
                        if (response.status === 200) {
                            // 克隆响应以避免消费原始响应流
                            const responseClone = response.clone();
                            responseClone.json().then(data => {
                                if (self.checkResponseForAuthFailure(data)) {
                                    self.handleAuthFailure('响应内容表示认证失败');
                                }
                            }).catch(() => {
                                // 忽略JSON解析错误，可能是HTML或其他格式
                            });
                        }
                    }

                    // 检查响应头中的新token信息
                    self.handleResponseHeaders(response);
                    return response;
                }).catch(error => {
                    // 网络错误或其他错误
                    throw error;
                });
            };
        },

        // 拦截XMLHttpRequest，自动添加认证头
        interceptXHR: function() {
            const originalOpen = XMLHttpRequest.prototype.open;
            const originalSend = XMLHttpRequest.prototype.send;
            const self = this;

            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._url = url;
                return originalOpen.apply(this, [method, url, ...args]);
            };

            XMLHttpRequest.prototype.send = function(data) {
                const xhr = this;

                // 只对本站API请求添加认证头，但排除登录API
                if (this._url && self.isLocalApiRequest(this._url) && !self.isLoginApiRequest(this._url)) {
                    const accessToken = self.getAccessToken();

                    if (accessToken) {
                        this.setRequestHeader('X-Admin-Access-Token', accessToken);
                        this.setRequestHeader('X-Admin-ID', self.getAdminId());
                        this.setRequestHeader('X-Admin-Role', self.getAdminRole());
                    } else {
                        // 如果没有token，让服务器验证
                    }
                } else if (this._url && self.isLoginApiRequest(this._url)) {
                    // 登录API请求，不添加认证头

                    // 添加状态变化监听器来检查认证失败
                    const originalOnReadyStateChange = this.onreadystatechange;
                    this.onreadystatechange = function() {
                        if (this.readyState === 4) { // 请求完成
                            // 只对本站API检查认证失败，排除登录API
                            if (self.isLocalApiRequest(xhr._url) && !self.isLoginApiRequest(xhr._url)) {


                                // 检查认证失败
                                if (this.status === 401 || this.status === 403) {

                                    self.handleAuthFailure('服务器返回认证失败');
                                } else if (this.status === 200) {
                                    // 检查响应内容是否表示认证失败
                                    try {
                                        const responseData = JSON.parse(this.responseText);
                                        if (self.checkResponseForAuthFailure(responseData)) {
                                            self.handleAuthFailure('响应内容表示认证失败');
                                        }
                                    } catch (e) {
                                        // 忽略JSON解析错误
                                    }
                                }
                            }
                        }

                        // 调用原始的状态变化处理器
                        if (originalOnReadyStateChange) {
                            originalOnReadyStateChange.apply(this, arguments);
                        }
                    };
                }

                return originalSend.apply(this, arguments);
            };
        },

        // 处理响应头中的token信息
        handleResponseHeaders: function(response) {
            try {
                const newAccessToken = response.headers.get('X-Admin-Access-Token');
                const newRefreshToken = response.headers.get('X-Admin-Refresh-Token');
                const adminId = response.headers.get('X-Admin-ID');
                const adminRole = response.headers.get('X-Admin-Role');

                if (newAccessToken) {
                    this.storeTokens(newAccessToken, newRefreshToken, adminId, adminRole);
                }
            } catch (e) {

            }
        },

        // 全局API响应检查器（供外部调用）
        checkApiResponse: function(response, responseData) {
            // 检查HTTP状态码
            if (response && this.isAuthFailureResponse(response)) {
                this.handleAuthFailure('API响应认证失败');
                return false;
            }

            // 检查响应内容
            if (responseData && this.checkResponseForAuthFailure(responseData)) {
                this.handleAuthFailure('API响应内容表示认证失败');
                return false;
            }

            return true;
        },

        // 处理登录响应（专门用于登录页面）
        handleLoginResponse: function(response) {
            if (response.headers) {
                const accessToken = response.headers.get('X-Admin-Access-Token');
                const refreshToken = response.headers.get('X-Admin-Refresh-Token');
                const adminId = response.headers.get('X-Admin-ID');
                const adminRole = response.headers.get('X-Admin-Role');

                if (accessToken) {
                    this.storeTokens(accessToken, refreshToken, adminId, adminRole);
                    return true;
                }
            }
            return false;
        },

        // 登出
        logout: function() {
            this.clearAuth();
            // 可以在这里添加登出API调用
            window.location.href = '/logon.html';
        },

        // 检查当前页面是否需要认证
        requiresAuth: function() {
            const currentPath = window.location.pathname;
            // 登录页面不需要认证
            if (currentPath.includes('/logon.html') ||
                currentPath.includes('/admin/login')) {
                return false;
            }
            // 其他后台页面都需要认证
            return currentPath.includes('/admin/') ||
                   currentPath.includes('/templates/') ||
                   currentPath.includes('admin') ||
                   currentPath.includes('manage');
        },

        // 页面加载时检查认证状态
        checkAuthOnPageLoad: function() {
            if (this.requiresAuth() && !this.isAuthenticated()) {
                this.handleAuthFailure('页面需要认证但未登录');
            }
        },


    };

    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            AdminAuth.init();
        });
    } else {
        AdminAuth.init();
    }

    // 暴露到全局
    window.AdminAuth = AdminAuth;



})(window);
