// 页面加载完成后初始化框架
        document.addEventListener('DOMContentLoaded', function() {
            // 隐藏加载遮罩
            setTimeout(function() {
                const loadingOverlay = document.getElementById('afLoadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('af-hidden');
                    setTimeout(function() {
                        loadingOverlay.style.display = 'none';
                    }, 300);
                }
            }, 500);
            
            // 初始化管理框架
            if (window.AdminFramework) {
                window.AdminFramework.init();
            }
        });