# 依思SUP

一个基于Python Django的现代化SUP系统，支持多环境部署和自动化配置管理。

## 🚀 快速开始

### 环境切换

```bash
# 开发环境（本地私密配置）
python dev_config.py
python run.py runserver

# 开发环境（通用配置）
python deploy.py development
python run.py runserver

# 预生产环境（Git提交前测试）
python deploy.py preproduction
python run.py runserver

# 生产环境
python deploy.py production 服务器IP 域名
python run.py runserver
```

## 🛠️ 技术栈

- **后端**: Django 5.1.4
- **数据库**: MySQL
- **前端**: HTML5, CSS3, JavaScript
- **部署**: 宝塔面板

## 📦 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt
```

## ⚙️ 配置说明

### 环境配置

项目支持三种环境模式：

| 环境 | DEBUG | ALLOWED_HOSTS | SECRET_KEY | 静态文件 | 用途 |
|------|-------|---------------|------------|----------|------|
| 开发环境 | True | 允许内网访问 | 开发用密钥 | 直接服务 | 本地开发调试 |
| 预生产环境 | False | 仅本地访问 | 安全密钥 | 收集到指定目录 | Git提交前测试 |
| 生产环境 | False | 指定域名/IP | 安全密钥 | 动态变更 | 正式部署 |

### 数据库配置

默认使用MySQL数据库，配置信息在 `.env` 文件中：

```env
DB_NAME=shopping_db
DB_USER=root
DB_PASSWORD=your-password
DB_HOST=localhost
DB_PORT=3306
```

### 🔥 智能防火墙自动配置

项目集成了智能防火墙自动配置功能，**无需手动配置系统防火墙**：
- 云服务器需要手动在服务商处开启对应端口的安全组权限

#### 功能特性
- ✅ **自动检测**：支持 firewalld、ufw、iptables 等主流防火墙

### 📁 智能静态文件管理

项目集成了智能静态文件管理功能，**自动处理生产环境静态文件收集和服务**：

#### 功能特性
- ✅ **自动收集**：生产环境启动时自动检测并收集静态文件
- ✅ **智能检测**：检测是否缺少项目静态文件，避免重复收集
- ✅ **Nginx检测**：智能检测是否配置了Nginx，自动决定静态文件服务策略
- ✅ **环境适配**：开发环境和生产环境使用不同的静态文件处理策略
- ✅ **故障诊断**：提供详细的静态文件诊断工具
- ✅ **智能判断**：自动判断是否需要配置防火墙
- ✅ **权限处理**：自动处理 sudo 权限问题
- ✅ **用户友好**：提供清晰的提示和确认流程
- ✅ **安全可控**：仅在必要时请求用户确认

#### 静态文件管理工作流程
1. **启动检查**：`python run.py runserver` 时自动检查静态文件状态
2. **智能判断**：检测是否需要收集静态文件（生产环境）
3. **自动收集**：如果需要，自动执行 `collectstatic` 命令
4. **Nginx检测**：检测是否配置了Nginx反向代理
5. **服务配置**：根据检测结果自动配置静态文件服务策略

#### 防火墙配置工作流程
1. **启动检查**：`python run.py runserver` 时自动检查防火墙状态
2. **智能判断**：如果检测到需要配置防火墙，会提示用户
3. **用户确认**：请求用户确认是否自动配置防火墙
4. **自动配置**：自动执行相应的防火墙配置命令
5. **验证结果**：配置完成后自动验证端口是否开放

#### 支持的系统
- **CentOS/RHEL**: firewalld
- **Ubuntu/Debian**: ufw
- **通用Linux**: iptables

#### 使用示例

**静态文件自动管理：**
```bash
# 启动服务器（会自动检查和收集静态文件）
python run.py runserver

# 生产环境输出示例：
# 步骤 4/5: 检查静态文件配置...
# 静态文件根目录: /www/wwwroot/project/staticfiles
# 检测到缺少项目静态文件，需要重新收集
# 正在收集静态文件...
# ✅ 静态文件收集完成
# 已收集的静态文件目录: ['admin', 'rest_framework', 'css', 'js', 'images']
# ✅ 项目静态文件收集成功: ['css', 'js', 'images']
# 🔧 生产环境：未检测到Nginx，Django提供静态文件服务
```

**静态文件故障诊断：**
```bash
# 如果遇到静态文件404问题，运行诊断脚本
python server_static_diagnosis.py

# 手动收集静态文件
python manage.py collectstatic --clear --noinput -v 2

# 测试静态文件查找
python test_static_files.py
```

**防火墙自动配置：**
```bash
# 启动服务器（会自动检查和配置防火墙）
python run.py runserver

# 输出示例：
# 启动前检查：正在检查防火墙配置...
# 🔧 检测到需要在firewalld防火墙中开放端口 8000
# ⚠️  配置防火墙需要管理员权限
# 是否允许自动配置防火墙开放端口 8000？(y/n): y
# 🔧 正在配置firewalld开放端口 8000...
# ✅ 防火墙端口 8000 配置成功
# ✅ 验证通过：端口 8000 已成功开放
```

### 开发环境配置说明

**本地私密配置 (dev_config.py):**
- 包含您的实际数据库、邮件等私密信息
- 文件已添加到 `.gitignore`，不会提交到Git
- 仅存在于您的本地开发环境
- 推荐用于日常开发

**通用配置 (deploy.py development):**
- 不包含私密信息，使用示例配置
- 适合新用户或临时配置
- 需要手动编辑 `.env` 文件填写实际信息

### 生产部署

```bash
# 1. 上传项目到服务器
# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置生产环境
python deploy.py production ************* yourdomain.com

# 4. 启动服务
python run.py runserver 8000
```

详细部署说明请参考 [docs/部署指南.md](docs/部署指南.md)

## 📝 常用命令

### 环境管理
```bash
python dev_config.py             # 配置本地开发环境（私密配置）
python deploy.py development     # 配置开发环境（通用配置）
python deploy.py preproduction   # 配置预生产环境
python deploy.py production      # 配置生产环境
python deploy.py status          # 查看当前配置
```

### 服务管理
```bash
python run.py runserver          # 启动服务（完整检查）
python run.py runserver 9000     # 指定端口
python run.py migrate            # 执行数据库迁移
python run.py deps               # 检查安装依赖
```

## 🔧 故障排除

### 常见问题

1. **内网无法访问**
   ```bash
   python deploy.py development
   python run.py runserver
   ```

2. **静态文件无法加载**
   ```bash
   python deploy.py collectstatic
   ```

3. **数据库连接失败**
   - 检查 `.env` 文件中的数据库配置
   - 确保MySQL服务正在运行
   - 验证数据库用户权限

4. **外网无法访问（防火墙问题）**
   ```bash
   # 项目会自动检查和配置防火墙，如果失败可以手动配置：

   # CentOS/RHEL (firewalld)
   sudo firewall-cmd --permanent --add-port=8000/tcp
   sudo firewall-cmd --reload

   # Ubuntu/Debian (ufw)
   sudo ufw allow 8000/tcp

   # 通用 (iptables)
   sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
   ```

5. **防火墙自动配置失败**
   - 确保有 sudo 权限
   - 检查防火墙服务是否正在运行
   - 手动执行上述防火墙配置命令
   - 联系系统管理员协助配置

## 📁 项目结构

```
ShoppingDjango/
├── ShoppingDjango/          # 项目配置
├── api/                     # API接口
├── app/                     # 后台管理
├── user/                    # 用户模块
├── static/                  # 静态文件
├── templates/               # 模板文件
├── media/                   # 媒体文件
├── utils/                   # 工具函数
├── docs/                    # 项目文档
├── deploy.py               # 部署配置脚本
├── dev_config.py           # 本地开发配置（不提交Git）
├── run.py                  # 启动脚本
├── requirements.txt        # 依赖列表
├── deploy_config.yaml      # 部署配置文件（提交时为空）
└── .env                    # 环境配置（不提交Git）
```

## 📤 Git提交流程

**安全的Git提交流程：**
```bash
# 1. 开发完成后，切换到预生产环境（自动清空敏感配置）
python deploy.py preproduction

# 2. 验证预生产环境正常（会有拦截提示）
python run.py runserver 8000

# 3. 安全提交到Git（配置文件已为空）
git add .
git commit -m "your commit message"
git push

# 4. 部署时重新配置生产环境
python deploy.py production [IP] [域名]
```

**文件提交状态：**
- ✅ `deploy_config.yaml` - 提交（预生产环境时为空）
- ❌ `dev_config.py` - 不提交（包含您的私密信息）
- ❌ `final_review_gate.py` - 不提交（开发工具）
- ❌ `.env` - 不提交（运行时配置）
- ❌ `.env.example` - 不提交（配置示例）

**🎯 推荐工作流程：**
1. **开发阶段**: `python dev_config.py` (本地私密配置)
2. **提交前测试**: `python deploy.py preproduction` (自动重置配置为空)
3. **Git提交**: 配置文件已清空，安全提交
4. **正式部署**: `python deploy.py production ************* example.com`

## 📚 项目文档

完整的项目文档位于 `docs/` 目录，包含：

### 🔐 安全相关
- [后台API安全改造任务文档](docs/后台API安全改造任务文档.md) - 企业级安全认证系统
- [API安全分析报告](docs/API安全分析报告.md) - 安全漏洞分析与修复
- [认证失效跳转功能说明](docs/认证失效跳转功能说明.md) - 自动登录跳转

### 📦 订单管理
- [订单管理API实现总结](docs/订单管理API实现总结.md) - 完整订单系统
- [订单列表页面API文档](docs/订单列表页面API文档.md) - API接口文档
- [订单数据JSON结构规范](docs/订单数据JSON结构规范.md) - 数据格式标准

### 🚀 部署运维
- [部署指南](docs/部署指南.md) - 详细部署说明
- [项目完成总结](docs/项目完成总结.md) - 开发总结报告

查看完整文档目录：[docs/README.md](docs/README.md)

## 🔒 安全说明

- 企业级JWT认证系统，支持多级安全验证
- 生产环境自动生成安全的SECRET_KEY
- 完整的API安全审计日志系统
- 数据库密码和邮件配置请及时修改
- 生产环境禁用DEBUG模式
- ALLOWED_HOSTS严格限制访问来源

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**快速解决内网访问问题**: `python deploy.py development && python run.py runserver`
