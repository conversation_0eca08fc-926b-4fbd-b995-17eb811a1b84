/**
 * 定价模板页面初始化 - MutationObserver优化版本
 * 使用DOM变化监听确保在单页应用中正确初始化
 */

// 全局变量声明 - 防止重复声明
if (typeof templates === 'undefined') var templates = [];
if (typeof memberLevels === 'undefined') var memberLevels = [];
if (typeof currentEditId === 'undefined') var currentEditId = null;
if (typeof isPercentage === 'undefined') var isPercentage = false;
if (typeof pageInitialized === 'undefined') var pageInitialized = false;
if (typeof domObserver === 'undefined') var domObserver = null;



/**
 * 主初始化函数 - 使用MutationObserver
 */
if (typeof initializePriceTemplatePage === 'undefined') {
function initializePriceTemplatePage() {
    if (pageInitialized) {
        return;
    }

    // 多重检查策略，确保快速初始化
    if (checkDOMReady()) {
        performInitialization();
    } else {
        startDOMObserver();

        // 添加快速重试机制
        setTimeout(() => {
            if (!pageInitialized && checkDOMReady()) {
                if (domObserver) {
                    domObserver.disconnect();
                    domObserver = null;
                }
                performInitialization();
            }
        }, 500); // 500ms后快速重试
    }
}
} // 结束条件声明

/**
 * 检查DOM是否准备好 - 简化版本
 */
function checkDOMReady() {
    // 核心元素 - 必须存在
    const coreElements = [
        'pricing-template-container',
        'templatesTableBody',
        'loading-overlay'
    ];

    // 检查核心元素是否存在
    const coreReady = coreElements.every(id => {
        const element = document.getElementById(id);
        if (!element) {
            return false;
        }
        return true;
    });

    if (!coreReady) {
        return false;
    }

    // 可选元素 - 存在更好，但不是必需的
    const optionalElements = ['saveTemplateBtn', 'templateModal'];
    let optionalCount = 0;

    optionalElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            optionalCount++;
        }
    });

    // 如果核心元素都存在，就认为DOM准备好了
    return true;
}

/**
 * 启动DOM变化观察器
 */
function startDOMObserver() {
    // 清理现有观察器
    if (domObserver) {
        domObserver.disconnect();
    }

    // 创建新的观察器 - 优化版本
    domObserver = new MutationObserver(function(mutations) {
        let shouldCheck = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // 检查是否有核心元素被添加
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 检查核心元素
                        const coreElements = ['pricing-template-container', 'templatesTableBody', 'loading-overlay'];
                        if (node.id && coreElements.includes(node.id)) {
                            shouldCheck = true;
                        }
                        // 检查子元素中是否包含核心元素
                        if (node.querySelector) {
                            for (const elementId of coreElements) {
                                if (node.querySelector(`#${elementId}`)) {
                                    shouldCheck = true;
                                    break;
                                }
                            }
                        }
                    }
                });
            }
        });

        if (shouldCheck) {
            // 缩短延迟时间，快速响应
            setTimeout(() => {
                if (!pageInitialized && checkDOMReady()) {
                    domObserver.disconnect();
                    domObserver = null;
                    performInitialization();
                }
            }, 50); // 缩短到50ms
        }
    });

    // 开始观察整个文档的变化
    domObserver.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 设置较短的超时时间，快速回退
    setTimeout(() => {
        if (!pageInitialized && domObserver) {
            domObserver.disconnect();
            domObserver = null;
            performInitialization();
        }
    }, 2000); // 2秒超时，提高响应速度
}

/**
 * 执行实际的初始化操作
 */
function performInitialization() {
    if (pageInitialized) {
        return;
    }

    try {
        // 初始化页面组件
        initPage();

        // 应用页面过渡效果
        applyPageTransition();

        // 延迟加载数据，确保页面组件完全初始化
        setTimeout(() => {
            loadTemplates();
            loadMemberLevels();
        }, 200);

        pageInitialized = true;

    } catch (error) {
        if (typeof showNotification === 'function') {
            showNotification('页面初始化失败，请刷新重试', 'error');
        }
    }
}

/**
 * 应用页面过渡效果
 */
function applyPageTransition() {
    const container = document.getElementById('pricing-template-container');
    if (container) {
        container.style.opacity = '0';
        container.style.transition = 'opacity 0.5s ease';

        setTimeout(() => {
            container.style.opacity = '1';
        }, 50);
    }
}

// 立即开始初始化
setTimeout(() => {
    initializePriceTemplatePage();
}, 50);

// 切换价格方式函数 - 在单选框点击时直接调用
function switchPriceMethod(method) {
    isPercentage = (method === 'percentage');
    
    // 添加动画效果
    const unitTexts = document.querySelectorAll('.unit-text');
    unitTexts.forEach(element => {
        element.style.transform = 'scale(0.8)';
        element.style.opacity = '0.5';
        
        setTimeout(() => {
            // 更新单位文本
            element.textContent = isPercentage ? '%' : '元';
            
            // 恢复并添加放大效果
            element.style.transform = 'scale(1.2)';
            element.style.opacity = '1';
            
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 150);
        }, 150);
    });
    
    // 重新计算示例
    calculateExample();
}

// 更新所有单位文本的专用函数
function updateAllUnitTexts() {
    const unitValue = isPercentage ? '%' : '元';
    
    // 更新所有单位文本元素
    document.querySelectorAll('.unit-text').forEach(element => {
        element.textContent = unitValue;
    });
}

// 签名工具函数
// 计算MD5哈希
function md5(string) {
    function cmn(q, a, b, x, s, t) {
        a = add32(add32(a, q), add32(x, t));
        return add32((a << s) | (a >>> (32 - s)), b);
    }

    function ff(a, b, c, d, x, s, t) {
        return cmn((b & c) | ((~b) & d), a, b, x, s, t);
    }

    function gg(a, b, c, d, x, s, t) {
        return cmn((b & d) | (c & (~d)), a, b, x, s, t);
    }

    function hh(a, b, c, d, x, s, t) {
        return cmn(b ^ c ^ d, a, b, x, s, t);
    }

    function ii(a, b, c, d, x, s, t) {
        return cmn(c ^ (b | (~d)), a, b, x, s, t);
    }

    function md5cycle(x, k) {
        let a = x[0], b = x[1], c = x[2], d = x[3];

        a = ff(a, b, c, d, k[0], 7, -680876936);
        d = ff(d, a, b, c, k[1], 12, -389564586);
        c = ff(c, d, a, b, k[2], 17, 606105819);
        b = ff(b, c, d, a, k[3], 22, -1044525330);
        a = ff(a, b, c, d, k[4], 7, -176418897);
        d = ff(d, a, b, c, k[5], 12, 1200080426);
        c = ff(c, d, a, b, k[6], 17, -1473231341);
        b = ff(b, c, d, a, k[7], 22, -45705983);
        a = ff(a, b, c, d, k[8], 7, 1770035416);
        d = ff(d, a, b, c, k[9], 12, -1958414417);
        c = ff(c, d, a, b, k[10], 17, -42063);
        b = ff(b, c, d, a, k[11], 22, -1990404162);
        a = ff(a, b, c, d, k[12], 7, 1804603682);
        d = ff(d, a, b, c, k[13], 12, -40341101);
        c = ff(c, d, a, b, k[14], 17, -1502002290);
        b = ff(b, c, d, a, k[15], 22, 1236535329);

        a = gg(a, b, c, d, k[1], 5, -165796510);
        d = gg(d, a, b, c, k[6], 9, -1069501632);
        c = gg(c, d, a, b, k[11], 14, 643717713);
        b = gg(b, c, d, a, k[0], 20, -373897302);
        a = gg(a, b, c, d, k[5], 5, -701558691);
        d = gg(d, a, b, c, k[10], 9, 38016083);
        c = gg(c, d, a, b, k[15], 14, -660478335);
        b = gg(b, c, d, a, k[4], 20, -405537848);
        a = gg(a, b, c, d, k[9], 5, 568446438);
        d = gg(d, a, b, c, k[14], 9, -1019803690);
        c = gg(c, d, a, b, k[3], 14, -187363961);
        b = gg(b, c, d, a, k[8], 20, 1163531501);
        a = gg(a, b, c, d, k[13], 5, -1444681467);
        d = gg(d, a, b, c, k[2], 9, -51403784);
        c = gg(c, d, a, b, k[7], 14, 1735328473);
        b = gg(b, c, d, a, k[12], 20, -1926607734);

        a = hh(a, b, c, d, k[5], 4, -378558);
        d = hh(d, a, b, c, k[8], 11, -2022574463);
        c = hh(c, d, a, b, k[11], 16, 1839030562);
        b = hh(b, c, d, a, k[14], 23, -35309556);
        a = hh(a, b, c, d, k[1], 4, -1530992060);
        d = hh(d, a, b, c, k[4], 11, 1272893353);
        c = hh(c, d, a, b, k[7], 16, -155497632);
        b = hh(b, c, d, a, k[10], 23, -1094730640);
        a = hh(a, b, c, d, k[13], 4, 681279174);
        d = hh(d, a, b, c, k[0], 11, -358537222);
        c = hh(c, d, a, b, k[3], 16, -722521979);
        b = hh(b, c, d, a, k[6], 23, 76029189);
        a = hh(a, b, c, d, k[9], 4, -640364487);
        d = hh(d, a, b, c, k[12], 11, -421815835);
        c = hh(c, d, a, b, k[15], 16, 530742520);
        b = hh(b, c, d, a, k[2], 23, -995338651);

        a = ii(a, b, c, d, k[0], 6, -198630844);
        d = ii(d, a, b, c, k[7], 10, 1126891415);
        c = ii(c, d, a, b, k[14], 15, -1416354905);
        b = ii(b, c, d, a, k[5], 21, -57434055);
        a = ii(a, b, c, d, k[12], 6, 1700485571);
        d = ii(d, a, b, c, k[3], 10, -1894986606);
        c = ii(c, d, a, b, k[10], 15, -1051523);
        b = ii(b, c, d, a, k[1], 21, -2054922799);
        a = ii(a, b, c, d, k[8], 6, 1873313359);
        d = ii(d, a, b, c, k[15], 10, -30611744);
        c = ii(c, d, a, b, k[6], 15, -1560198380);
        b = ii(b, c, d, a, k[13], 21, 1309151649);
        a = ii(a, b, c, d, k[4], 6, -145523070);
        d = ii(d, a, b, c, k[11], 10, -1120210379);
        c = ii(c, d, a, b, k[2], 15, 718787259);
        b = ii(b, c, d, a, k[9], 21, -343485551);

        x[0] = add32(a, x[0]);
        x[1] = add32(b, x[1]);
        x[2] = add32(c, x[2]);
        x[3] = add32(d, x[3]);
    }

    function md5blk(s) {
        let md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
            md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
        }
        return md5blks;
    }

    function md5blk_array(a) {
        let md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
            md5blks[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);
        }
        return md5blks;
    }

    function md51(s) {
        let n = s.length,
            state = [1732584193, -271733879, -1732584194, 271733878], i;
        for (i = 64; i <= s.length; i += 64) {
            md5cycle(state, md5blk(s.substring(i - 64, i)));
        }
        s = s.substring(i - 64);
        let tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < s.length; i++)
            tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);
        tail[i >> 2] |= 0x80 << ((i % 4) << 3);
        if (i > 55) {
            md5cycle(state, tail);
            for (i = 0; i < 16; i++) tail[i] = 0;
        }
        tail[14] = n * 8;
        md5cycle(state, tail);
        return state;
    }

    function md51_array(a) {
        let n = a.length,
            state = [1732584193, -271733879, -1732584194, 271733878], i;
        for (i = 64; i <= a.length; i += 64) {
            md5cycle(state, md5blk_array(a.subarray(i - 64, i)));
        }
        a = (i - 64) < a.length ? a.subarray(i - 64) : new Uint8Array(0);
        let tail = new Uint8Array(64), len = a.length;
        for (i = 0; i < len; i++)
            tail[i] = a[i];
        tail[len] = 0x80;
        if (len > 55) {
            md5cycle(state, tail);
            for (i = 0; i < 64; i++) tail[i] = 0;
        }
        tail[14] = n * 8;
        md5cycle(state, tail);
        return state;
    }

    function hex_md5(s) {
        let result = "";
        for (let i = 0; i < 4; i++)
            result += hex_chr[(md51(s)[i] >> 4) & 0x0F] + hex_chr[md51(s)[i] & 0x0F] + hex_chr[(md51(s)[i] >> 12) & 0x0F] + hex_chr[(md51(s)[i] >> 8) & 0x0F] + hex_chr[(md51(s)[i] >> 20) & 0x0F] + hex_chr[(md51(s)[i] >> 16) & 0x0F] + hex_chr[(md51(s)[i] >> 28) & 0x0F] + hex_chr[(md51(s)[i] >> 24) & 0x0F];
        return result;
    }

    function hex_hmac_md5(key, data) { return hex_md5(key + data); }

    function add32(a, b) {
        return (a + b) & 0xFFFFFFFF;
    }

    let hex_chr = '0123456789abcdef'.split('');
    return hex_md5(string);
}

// Base64编码函数
function base64Encode(str) {
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
        return String.fromCharCode('0x' + p1);
    }));
}

// 计算API请求签名
function calculateSign(data) {
    // 创建数据的深拷贝，避免修改原始数据
    const dataCopy = JSON.parse(JSON.stringify(data));
    
    // 删除sign字段(如果存在)
    if (dataCopy.sign !== undefined) {
        delete dataCopy.sign;
    }
    
    // 递归处理对象，确保所有级别的键都按ASCII排序
    function sortObjectKeys(obj) {
        // 如果不是对象或是数组，直接返回
        if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
            return obj;
        }
        
        // 创建新的有序对象
        const sortedObj = {};
        // 获取所有键并排序
        const keys = Object.keys(obj).sort();
        
        // 填充排序后的对象
        for (const key of keys) {
            sortedObj[key] = sortObjectKeys(obj[key]);
        }
        
        return sortedObj;
    }
    
    // 对整个对象进行排序处理
    const sortedData = sortObjectKeys(dataCopy);
    
    // 使用与后端完全匹配的JSON.stringify参数
    // 后端使用: separators=(',', ':'), sort_keys=True, ensure_ascii=False
    
    // 使用JSON.stringify并模拟后端的separators选项
    let jsonStr = JSON.stringify(sortedData);
    
    // 模拟Python的separators=(',', ':')
    // 移除空白字符并确保键值对之间只有逗号，键与值之间只有冒号
    jsonStr = jsonStr.replace(/\s+/g, '')             // 移除所有空白字符
                     .replace(/,"/g, ',"')            // 确保逗号后面没有空白
                     .replace(/":/g, '":');           // 确保冒号前后没有空白
    
    // Base64编码
    const base64Str = base64Encode(jsonStr);

    // 计算MD5哈希
    const sign = md5(base64Str);
    
    return sign;
}



/**
 * 页面核心初始化函数
 * 企业级错误处理和DOM安全检查
 */
function initPage() {
    try {
        // 验证关键DOM元素
        if (!validateRequiredElements()) {
            throw new Error('关键DOM元素验证失败');
        }

        // 设置事件监听器
        setupEventListeners();

        // 添加表格交互效果
        addTableRowEffects();

        return true;

    } catch (error) {
        showNotification('页面初始化失败', 'error');
        return false;
    }
}

/**
 * 验证页面必需的DOM元素
 */
function validateRequiredElements() {
    const requiredElements = [
        { id: 'templatesTableBody', name: '模板表格容器' },
        { id: 'saveTemplateBtn', name: '保存按钮' },
        { id: 'templateModal', name: '模态框' },
        { id: 'loading-overlay', name: '加载遮罩' }
    ];

    for (const element of requiredElements) {
        if (!document.getElementById(element.id)) {
            return false;
        }
    }

    return true;
}

// 添加表格行悬停效果
function addTableRowEffects() {
    // 使用事件委托为表格行添加悬停效果
    const tableBody = document.getElementById('templatesTableBody');
    if (tableBody) {
        tableBody.addEventListener('mouseover', function(e) {
            const row = e.target.closest('tr');
            if (row && !row.querySelector('.empty-state')) {
                row.style.transition = 'transform 0.2s ease';
                row.style.transform = 'translateY(-2px)';
                row.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.05)';
            }
        });
        
        tableBody.addEventListener('mouseout', function(e) {
            const row = e.target.closest('tr');
            if (row && !row.querySelector('.empty-state')) {
                row.style.transform = 'translateY(0)';
                row.style.boxShadow = 'none';
            }
        });
    }
}

// 设置事件监听
function setupEventListeners() {
    // 测试添加模板按钮是否可点击
    const addBtn = document.getElementById('addTemplateBtn');
    if (addBtn) {
        // 添加额外的事件监听器作为备用
        if (!addBtn.hasAttribute('data-event-bound')) {
            addBtn.addEventListener('click', function(e) {
                if (typeof openModal === 'function') {
                    openModal();
                }
            });
            addBtn.setAttribute('data-event-bound', 'true');
        }
    }

    // 设置保存按钮事件 - 防止重复绑定
    const saveBtn = document.getElementById('saveTemplateBtn');
    if (saveBtn && !saveBtn.hasAttribute('data-save-bound')) {
        saveBtn.addEventListener('click', saveTemplate);
        saveBtn.setAttribute('data-save-bound', 'true');
    }

    // 设置计算示例按钮事件
    const calculateBtn = document.querySelector('button[onclick="calculateExample()"]');
    if (calculateBtn) {
        calculateBtn.onclick = function(e) {
            e.preventDefault();

            // 添加按钮点击效果
            this.classList.add('clicking');
            setTimeout(() => {
                this.classList.remove('clicking');
            }, 200);

            calculateExample();
            return false;
        };
    }

    // 确认删除对话框按钮
    const cancelDeleteBtn = document.getElementById('cancelDelete');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            document.getElementById('confirmDialog').classList.remove('show');
        });
    }

    // 给输入框添加焦点效果
    document.querySelectorAll('.form-control').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('input-focus');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('input-focus');
        });
    });
}

// 模态框控制函数 - 增强版本
if (typeof openModal === 'undefined') {
function openModal() {
    try {
        // 先重置表单
        if (typeof resetForm === 'function') {
            resetForm();
        }

        // 显示模态框
        const templateModal = document.getElementById('templateModal');
        if (templateModal) {
            templateModal.style.display = 'flex';
            templateModal.style.opacity = '0';

            // 添加淡入效果
            setTimeout(() => {
                templateModal.style.opacity = '1';
            }, 50);

            // 设置标题为添加模板
            const modalTitle = templateModal.querySelector('.modal-title');
            if (modalTitle) {
                modalTitle.innerHTML = '<i class="fas fa-plus-circle"></i> 添加定价模板';
            }
        } else {
            alert('系统错误：无法打开添加模板对话框');
            return;
        }

        // 加载会员等级数据
        if (typeof loadMemberLevels === 'function') {
            loadMemberLevels();
        }

        // 确保单位文本显示正确
        setTimeout(function() {
            try {
                // 获取当前单选按钮状态
                const percentageRadio = document.querySelector('input[name="priceMethod"][value="percentage"]');
                if (percentageRadio) {
                    isPercentage = percentageRadio.checked;
                } else {
                    isPercentage = false;
                }

                if (typeof updateAllUnitTexts === 'function') {
                    updateAllUnitTexts();
                }

                if (typeof calculateExample === 'function') {
                    calculateExample();
                }
            } catch (error) {
                // 静默处理错误
            }
        }, 100);

    } catch (error) {
        alert('打开添加模板对话框时发生错误');
    }
}
} // 结束openModal条件声明

function closeModal() {
    // 隐藏模态框
    const templateModal = document.getElementById('templateModal');
    if (templateModal) {
        templateModal.style.display = 'none';
    }

    // 重置表单
    resetForm();
}

// 更明确的重置表单函数
function resetForm() {
    
    // 重置基本字段
    const templateId = document.getElementById('templateId');
    const templateName = document.getElementById('templateName');
    
    if (templateId) templateId.value = '';
    if (templateName) templateName.value = '';
    
    // 重置价格方式为固定金额，并更新全局状态
    isPercentage = false;
    const fixedRadio = document.querySelector('input[name="priceMethod"][value="fixed"]');
    if (fixedRadio) {
        fixedRadio.checked = true;
    }
    
    // 重置所有价格输入为0
    document.querySelectorAll('.level-price').forEach(input => {
        input.value = '0';
    });
    
    // 重置当前编辑ID
    currentEditId = null;
    
    // 更新单位文本
    updateAllUnitTexts();
}

/**
 * 加载会员等级数据 - 简化版本
 */
function loadMemberLevels() {
    const memberLevelInputs = document.getElementById('memberLevelInputs');
    if (!memberLevelInputs) {
        return;
    }

    // 显示加载状态
    showMemberLevelLoading(true);

    // 调用API
    const apiUrl = '/api/GetMemberLevelList';

    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data && data.code === 200 && Array.isArray(data.data)) {
            memberLevels = data.data;
            renderMemberLevelInputs();

            // 如果当前在编辑模式，重新计算示例
            if (currentEditId) {
                calculateExample();
            }
        } else {
            memberLevels = [];
            renderMemberLevelInputs();
        }
    })
    .catch(error => {
        memberLevels = [];
        renderMemberLevelInputs();
    })
    .finally(() => {
        showMemberLevelLoading(false);
    });
}

/**
 * 会员等级加载状态管理
 */
function showMemberLevelLoading(show) {
    const memberLevelInputs = document.getElementById('memberLevelInputs');
    if (!memberLevelInputs) return;

    if (show) {
        memberLevelInputs.innerHTML = `
            <div id="loadingMemberLevels" style="text-align: center; padding: 20px;">
                <div class="spinner" style="width: 30px; height: 30px; margin: 0 auto;"></div>
                <div style="margin-top: 10px; color: var(--primary-color);">加载会员等级中...</div>
            </div>
        `;
    }
}

// 渲染会员等级输入
function renderMemberLevelInputs() {
    const memberLevelInputs = document.getElementById('memberLevelInputs');
    
    // 清空现有内容
    memberLevelInputs.innerHTML = '';
    
    if (!memberLevels || memberLevels.length === 0) {
        memberLevelInputs.innerHTML = '<div style="padding: 15px; color: #666; text-align: center;">暂无会员等级数据</div>';
        return;
    }

    // 创建会员等级输入 - 使用全局isPercentage变量
    const unitText = isPercentage ? '%' : '元';
    
    for (const level of memberLevels) {
        const levelDiv = document.createElement('div');
        levelDiv.className = 'member-level-item';
        levelDiv.style.cssText = 'border: 1px solid #eee; border-radius: 6px; padding: 15px; background: #f9f9f9; margin-bottom: 15px; transition: all 0.2s ease;';
        
        levelDiv.innerHTML = `
            <label class="form-label" style="margin-bottom: 10px; font-weight: 500; color: #444;">${level.name}</label>
            <div class="input-group">
                <input type="number" data-level-id="${level.id}" class="form-control level-price" min="0" step="0.01" value="0">
                <span class="input-group-text unit-text">${unitText}</span>
            </div>
        `;
        
        // 添加鼠标悬停效果
        levelDiv.addEventListener('mouseover', function() {
            this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            this.style.borderColor = 'var(--primary-light)';
        });
        
        levelDiv.addEventListener('mouseout', function() {
            this.style.boxShadow = 'none';
            this.style.borderColor = '#eee';
        });
        
        memberLevelInputs.appendChild(levelDiv);
    }
    
    // 更新普通用户输入框单位
    updateAllUnitTexts();
    
    // 初始计算示例
    calculateExample();
}

/**
 * 更新单位显示 - 简化版本
 */
function updateUnitDisplay() {
    const unitElements = document.querySelectorAll('.price-unit');
    const unitText = isPercentage ? '%' : '元';

    unitElements.forEach(element => {
        element.textContent = unitText;
    });

    calculateExample();
}

/**
 * 计算价格示例
 * 企业级计算逻辑和错误处理
 */
function calculateExample() {
    try {
        // 获取当前价格方式
        const percentageSelected = document.querySelector('input[name="priceMethod"][value="percentage"]')?.checked || false;
        isPercentage = percentageSelected;

        // 获取基础价格
        const basePriceInput = document.getElementById('sampleBasePrice');
        const basePrice = parseFloat(basePriceInput?.value) || 0;

        if (basePrice < 0) {
            throw new Error('基础价格不能为负数');
        }

        // 构建结果
        const results = [`基础价格: ${basePrice.toFixed(2)}元`];

        // 计算普通用户价格
        const defaultInput = document.querySelector('.level-price[data-level-id="default"]');
        if (defaultInput) {
            const value = parseFloat(defaultInput.value) || 0;
            const finalPrice = isPercentage
                ? basePrice * (1 + value / 100)
                : basePrice + value;

            const unit = isPercentage ? '%' : '元';
            const action = isPercentage ? '加价' : '加';
            results.push(`普通用户价格: ${finalPrice.toFixed(2)}元 (${action}${value}${unit})`);
        }

        // 计算会员等级价格
        document.querySelectorAll('.level-price:not([data-level-id="default"])').forEach(input => {
            const levelId = input.getAttribute('data-level-id');
            const level = memberLevels?.find(l => l.id === levelId);
            if (!level) return;

            const value = parseFloat(input.value) || 0;
            const finalPrice = isPercentage
                ? basePrice * (1 + value / 100)
                : basePrice + value;

            const unit = isPercentage ? '%' : '元';
            const action = isPercentage ? '加价' : '加';
            results.push(`${level.name}价格: ${finalPrice.toFixed(2)}元 (${action}${value}${unit})`);
        });

        // 更新显示
        const resultsContainer = document.getElementById('calculationResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = results.map(result => `<div>${result}</div>`).join('');
        }

    } catch (error) {
        const resultsContainer = document.getElementById('calculationResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = '<div style="color: #ff6b6b;">计算失败，请检查输入数据</div>';
        }
    }
}

/**
 * 加载定价模板列表 - 简化版本
 */
function loadTemplates() {
    // 验证必要的DOM元素
    const loadingOverlay = document.getElementById('loading-overlay');
    const tableBody = document.getElementById('templatesTableBody');

    if (!loadingOverlay || !tableBody) {
        return;
    }

    // 显示加载状态
    showLoadingState(true);

    // 调用API
    const apiUrl = '/api/GetPriceTemplateList';

    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data && data.code === 200 && Array.isArray(data.data)) {
            templates = data.data;
            renderTemplateTable();
        } else {
            const errorMsg = data?.msg || '获取模板数据失败';
            showNotification(errorMsg, 'error');
            templates = [];
            renderTemplateTable();
        }
    })
    .catch(error => {
        showNotification('加载模板列表失败，请检查网络连接', 'error');
        templates = [];
        renderTemplateTable();
    })
    .finally(() => {
        showLoadingState(false);
    });
}

/**
 * 统一的加载状态管理
 */
function showLoadingState(show) {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    }
}

// 渲染模板表格
function renderTemplateTable() {
    const tableBody = document.getElementById('templatesTableBody');

    if (!tableBody) {
        return;
    }

    if (!templates || templates.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5">
                    <div class="empty-state">
                        <i class="fas fa-tags"></i>
                        <h3>暂无定价模板</h3>
                        <p>点击"添加模板"按钮创建您的第一个定价模板</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    let html = '';

    try {
        for (const template of templates) {
            // 根据API返回的type字段判断价格方式 (1: 固定金额, 2: 百分比)
            const priceMethod = template.type === "1" ? "fixed" : "percentage";
            const badge = priceMethod === 'fixed' 
                ? '<span class="badge badge-fixed">固定金额</span>' 
                : '<span class="badge badge-percentage">百分比</span>';
    
            html += `
            <tr>
                <td>${template.id || '无ID'}</td>
                <td>${template.name || '未命名'}</td>
                <td>${badge}</td>
                <td>
                    <div class="price-sample">
                        基础价格: 100元<br>
                        样例价格: <span class="price-result">${getSamplePrice(template)}</span>
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="editTemplate(${JSON.stringify(template).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="showDeleteConfirmation(${JSON.stringify(template).replace(/"/g, '&quot;')})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            `;
        }

        tableBody.innerHTML = html;
    } catch (error) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5">
                    <div class="empty-state" style="color: #ff3e5f;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>渲染表格时出错</h3>
                        <p>${error.message}</p>
                    </div>
                </td>
            </tr>
        `;
    }
}

// 获取样例价格
function getSamplePrice(template) {
    if (!template || !template.data) return "0.00元";
    
    const basePrice = 100;
    let price = 0;
    
    // 首先尝试获取普通用户价格
    const normalUserValue = template.data.NormalUser !== undefined ? template.data.NormalUser : 0;
    
    // 根据价格类型计算
    if (template.type === "1") { // 固定金额
        price = basePrice + parseFloat(normalUserValue);
    } else { // 百分比
        price = basePrice * (1 + parseFloat(normalUserValue) / 100);
    }
    
    return price.toFixed(2) + "元";
}

// 编辑模板 - 调整以匹配API数据结构
function editTemplate(template) {
    // 设置当前编辑ID
    currentEditId = template.id;

    // 先打开模态框，初始化一个干净的状态
    openModal();

    // 设置表单字段
    document.getElementById('templateId').value = template.id;
    document.getElementById('templateName').value = template.name;

    // 设置价格方式 (1: 固定金额, 2: 百分比)
    const priceMethod = template.type === "1" ? "fixed" : "percentage";

    // 直接设置价格方式并选中相应单选按钮
    isPercentage = (priceMethod === 'percentage');
    
    // 选中正确的单选按钮
    const radioElement = document.querySelector(`input[name="priceMethod"][value="${priceMethod}"]`);
    if (radioElement) {
        radioElement.checked = true;
    }
    
    // 立即更新所有单位文本
    updateAllUnitTexts();
    
    // 更新模态框标题
    document.querySelector('.modal-title').textContent = '编辑定价模板';
    
    // 在会员等级加载完成后填充数据
    const fillLevelValues = function() {
        if (!memberLevels) {
            // 如果会员等级未加载完成，延迟尝试
            setTimeout(fillLevelValues, 100);
            return;
        }
        
        // 确保数据对象存在
        if (!template.data) {
            return;
        }
        
        // 设置普通用户价格
        const defaultInput = document.querySelector('.level-price[data-level-id="default"]');
        if (defaultInput && template.data.NormalUser !== undefined) {
            defaultInput.value = template.data.NormalUser;
        }
        
        // 设置其他会员等级价格
        document.querySelectorAll('.level-price:not([data-level-id="default"])').forEach(input => {
            const levelId = input.getAttribute('data-level-id');
            if (levelId && template.data[levelId] !== undefined) {
                input.value = template.data[levelId];
            } else {
                input.value = 0; // 默认值
            }
        });
        
        // 更新示例计算
        calculateExample();
    };
    
    // 延迟调用，确保会员等级已加载
    setTimeout(fillLevelValues, 100);
}

// 显示删除确认
function showDeleteConfirmation(template) {
    
    const confirmDialog = document.getElementById('confirmDialog');
    const confirmMessage = confirmDialog.querySelector('.confirm-message');
    
    // 设置确认消息
    confirmMessage.textContent = `确定要删除"${template.name}"模板吗？此操作无法撤销。`;
    
    // 设置删除按钮事件
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    confirmDeleteBtn.onclick = function() {
        deleteTemplate(template.id);
        confirmDialog.classList.remove('show');
    };
    
    // 设置取消按钮事件
    const cancelDeleteBtn = document.getElementById('cancelDelete');
    cancelDeleteBtn.onclick = function() {
        confirmDialog.classList.remove('show');
    };
    
    // 显示确认对话框
    confirmDialog.classList.add('show');
}

// 删除模板
function deleteTemplate(id) {
    // 显示加载遮罩
    document.getElementById('loading-overlay').style.display = 'flex';

    // 构建请求数据
    const requestData = {
        id: id
    };

    // 计算签名并添加到请求数据中
    requestData.sign = calculateSign(requestData);
    
    // 发送API请求
    fetch('/api/DelPriceTemplate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        return response.json();
    })
    .then(response => {
        // 隐藏加载遮罩
        document.getElementById('loading-overlay').style.display = 'none';
        
        if (response.code === 200) {
            // 成功处理
            // 重新加载模板列表，确保数据最新
            loadTemplates();
            
            // 显示成功通知
            showNotification('模板已成功删除', 'success');
        } else {
            // 失败处理
            showNotification(response.msg || '删除失败，请重试', 'error');
        }
    })
    .catch(error => {
        // 隐藏加载遮罩
        document.getElementById('loading-overlay').style.display = 'none';

        // 显示错误通知
        showNotification('删除失败，请检查网络连接', 'error');
    });
}

// 保存模板
function saveTemplate() {
    // 防止重复提交
    const saveButton = document.getElementById('saveTemplateBtn');
    if (saveButton.disabled) {
        return;
    }

    // 获取表单数据
    const id = document.getElementById('templateId').value;
    const name = document.getElementById('templateName').value.trim();
    const priceMethod = document.querySelector('input[name="priceMethod"]:checked').value;
    
    // 验证表单
    if (!name) {
        showNotification('请输入模板名称', 'error');
        return;
    }
    
    // 构建API所需的data对象
    const data = {};
    
    const defaultInput = document.querySelector('.level-price[data-level-id="default"]');
    if (defaultInput) {
        data["NormalUser"] = parseFloat(defaultInput.value) || 0;
    } else {
        data["NormalUser"] = 0; // 确保始终有默认用户等级
    }
    
    // 添加其他会员等级价格
    document.querySelectorAll('.level-price:not([data-level-id="default"])').forEach(input => {
        const levelId = input.getAttribute('data-level-id');
        if (levelId && levelId !== "default") {
            const value = parseFloat(input.value) || 0;
            data[levelId] = value;
        }
    });
    
    // 按照API构建请求数据 - 按照API文档映射价格方式
    const requestData = {
        name: name,
        type: priceMethod === 'fixed' ? "1" : "2", // 1: 固定金额加价 2: 百分比加价
        data: data
    };
    
    // 如果是编辑现有模板，添加id字段
    if (id) {
        requestData.id = id;
    }
    
    // 计算并添加签名
    requestData.sign = calculateSign(requestData);
    
    // 显示保存中状态（使用已声明的saveButton变量）
    const saveSpinner = document.getElementById('saveSpinner');
    const saveButtonText = document.getElementById('saveButtonText');
    
    saveButton.disabled = true;
    saveSpinner.style.display = 'inline-block';
    saveButtonText.textContent = id ? '更新中...' : '保存中...';
    
    // 发送API请求
    fetch('/api/AddPriceTemplate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        return response.json();
    })
    .then(response => {
        // 恢复按钮状态
        saveButton.disabled = false;
        saveSpinner.style.display = 'none';
        saveButtonText.textContent = '保存模板';
        
        if (response.code === 200) {
            // 成功处理
            // 如果是新增，使用返回的ID
            if (!id && response.id) {
                requestData.id = response.id;
            }
            
            // 关闭模态框
            closeModal();
            
            // 重新加载模板列表，确保数据最新
            loadTemplates();
            
            // 显示成功通知
            showNotification(id ? '模板已成功更新' : '模板已成功创建', 'success');
        } else {
            // 失败处理
            showNotification(response.msg || '保存失败，请重试', 'error');
        }
    })
    .catch(error => {
        // 恢复按钮状态
        saveButton.disabled = false;
        saveSpinner.style.display = 'none';
        saveButtonText.textContent = '保存模板';

        // 显示错误通知
        showNotification('保存失败，请检查网络连接', 'error');
    });
}

// 显示通知
function showNotification(message, type) {
    try {
        const notification = document.getElementById('notification');
        if (!notification) {
            return;
        }

        const notificationMessage = document.getElementById('notificationMessage');
        if (!notificationMessage) {
            return;
        }

        const notificationIcon = notification.querySelector('.notification-icon');
        if (!notificationIcon) {
            return;
        }
        
        // 设置通知类型和图标
        notification.className = 'notification';
        notification.classList.add(type);
        
        if (type === 'success') {
            notificationIcon.className = 'notification-icon fas fa-check-circle';
        } else if (type === 'error') {
            notificationIcon.className = 'notification-icon fas fa-exclamation-circle';
        }
        
        // 设置通知消息
        notificationMessage.textContent = message;
        
        // 显示通知
        notification.classList.add('show');

        // 3秒后自动隐藏
        setTimeout(function() {
            notification.classList.remove('show');
        }, 3000);
    } catch (error) {
        // 如果通知系统失败，退而求其次使用alert
        if (type === 'error') {
            alert('错误: ' + message);
        }
    }
}