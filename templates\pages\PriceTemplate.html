<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="/static/css/pages/PriceTemplate.css">



<div id="pricing-template-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <div class="page-title">
        </div>
    </div>

    <!-- 加载中遮罩层 -->
    <div id="loading-overlay" style="display: none;">
        <div class="spinner"></div>
        <div style="margin-top: 15px; color: var(--primary-dark);">正在加载数据...</div>
    </div>

    <!-- 模板列表卡片 -->
    <div class="templates-card">
        <div class="card-title">
            <div style="font-size: 24px; font-weight: 600;">定价模板</div>
            <button id="addTemplateBtn" class="add-template-btn" onclick="openModal()">
                <i class="fas fa-plus"></i> 添加模板
            </button>
        </div>

        <table class="templates-table">
            <thead>
                <tr>
                    <th width="15%"><i class="fas fa-id-card"></i> 模板ID</th>
                    <th width="25%"><i class="fas fa-tag"></i> 模板名称</th>
                    <th width="15%"><i class="fas fa-money-bill-wave"></i> 加价方式</th>
                    <th width="30%"><i class="fas fa-calculator"></i> 计算示例</th>
                    <th width="15%"><i class="fas fa-cog"></i> 操作</th>
                </tr>
            </thead>
            <tbody id="templatesTableBody">
                <!-- 表格内容将通过JavaScript动态生成 -->
                <!-- 空状态 -->
                <tr>
                    <td colspan="5">
                        <div class="empty-state">
                            <i class="fas fa-tags"></i>
                            <h3>暂无定价模板</h3>
                            <p>点击"添加模板"按钮创建您的第一个定价模板</p>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 添加/编辑模板模态框 - 卡通可爱风格 -->
    <div id="templateModal">
        <div class="modal-content">
            <!-- 标题栏 -->
            <div class="modal-header">
                <h3 class="modal-title"><i class="fas fa-plus-circle"></i> 添加定价模板</h3>
                <button onclick="closeModal()">&times;</button>
            </div>

            <!-- 表单内容 -->
            <div class="modal-body">
                <form id="templateForm">
                    <input type="hidden" id="templateId" value="">
                    
                    <!-- 基本信息 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-bookmark" style="color: var(--primary-color); margin-right: 8px;"></i>
                            模板名称
                        </label>
                        <input type="text" id="templateName" class="form-control" placeholder="给您的定价模板起个名字吧">
                    </div>

                    <!-- 价格方式选择 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-sliders-h" style="color: var(--primary-color); margin-right: 8px;"></i>
                            价格方式
                        </label>
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="priceMethod" value="fixed" class="radio-input" checked onclick="switchPriceMethod('fixed')">
                                <span class="radio-control"></span>
                                <span class="radio-label">固定金额</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="priceMethod" value="percentage" class="radio-input" onclick="switchPriceMethod('percentage')">
                                <span class="radio-control"></span>
                                <span class="radio-label">百分比</span>
                            </label>
                        </div>
                    </div>

                    <!-- 会员等级价格设置 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-users" style="color: var(--primary-color); margin-right: 8px;"></i>
                            会员等级价格设置
                        </label>
                        
                        <!-- 添加固定的普通用户输入框 -->
                        <div class="member-level-item">
                            <label class="form-label" style="margin-bottom: 10px; font-weight: 500; color: #444;">
                                <i class="fas fa-user" style="color: var(--primary-color); margin-right: 5px;"></i>
                                普通用户 <span style="color: var(--primary-color); font-size: 12px;">(默认)</span>
                            </label>
                            <div class="input-group">
                                <input type="number" data-level-id="default" class="form-control level-price" min="0" step="0.01" value="0">
                                <span class="input-group-text unit-text">元</span>
                            </div>
                        </div>
                        
                        <div id="memberLevelInputs">
                            <div id="loadingMemberLevels" style="text-align: center; padding: 20px; display: none;">
                                <div class="spinner" style="width: 30px; height: 30px;"></div>
                                <div style="margin-top: 10px; color: var(--primary-color);">加载会员等级中...</div>
                            </div>
                            <!-- 会员等级输入将在这里动态添加 -->
                        </div>
                    </div>

                    <!-- 计算示例 -->
                    <div class="sample-calculation">
                        <h4>计算示例</h4>
                        <div class="form-group">
                            <div class="input-group" style="margin-bottom: 10px;">
                                <input type="number" id="sampleBasePrice" class="form-control" placeholder="输入基础价格" value="100">
                                <button type="button" class="btn btn-primary" onclick="calculateExample()" style="margin-left: 10px; border-radius: 10px;">
                                    <i class="fas fa-calculator"></i> 计算
                                </button>
                            </div>
                            <div id="calculationResults">
                                <!-- 计算结果将在这里显示 -->
                                <div>请点击"计算"按钮查看价格计算示例</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 底部按钮 -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" id="saveTemplateBtn">
                    <span id="saveSpinner" class="spinner" style="width: 18px; height: 18px; display: none;"></span>
                    <i class="fas fa-save" id="saveIcon"></i>
                    <span id="saveButtonText">保存模板</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 确认删除对话框 -->
    <div id="confirmDialog" class="confirm-dialog">
        <div class="confirm-box">
            <div class="confirm-title">
                <i class="fas fa-exclamation-triangle"></i> 确认删除
            </div>
            <div class="confirm-message">
                确定要删除这个定价模板吗？此操作无法撤销。
            </div>
            <div class="confirm-actions">
                <button id="cancelDelete" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button id="confirmDelete" class="btn btn-danger">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="notification">
        <i class="notification-icon fas fa-check-circle"></i>
        <span id="notificationMessage"></span>
    </div>
</div>

<script src="/static/js/pages/PriceTemplate.js"></script>

