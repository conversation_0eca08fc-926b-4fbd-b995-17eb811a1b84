from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.template.loader import get_template
from django.template.exceptions import TemplateDoesNotExist
from django.utils import timezone
import os
import json

# 导入智能模板路由器
from utils.template_router import smart_render


# Create your views here.


def index(request):
    # locals函数可以将该函数中出现过的所有变量传入到展示页面中，即index.html文件中
    return render(request, 'index.html', locals())

# 添加处理页面的视图
def page_view(request, page_name):
    """
    处理动态页面请求，尝试渲染请求的页面，如果不存在则渲染404页面
    """
    # 检查请求URL，确定正确的模板路径
    if request.path.startswith('/pages/'):
        template_path = f'pages/{page_name}'
    else:
        # 如果是根路径下的请求，直接使用页面名称
        template_path = page_name
    
    try:
        # 检查模板文件是否存在
        from django.template.loader import get_template
        from django.template.exceptions import TemplateDoesNotExist
        try:
            get_template(template_path)
            return render(request, template_path, locals())
        except TemplateDoesNotExist:
            # 如果模板不存在，尝试在pages目录下查找
            if not template_path.startswith('pages/'):
                try:
                    get_template(f'pages/{page_name}')
                    return render(request, f'pages/{page_name}', locals())
                except TemplateDoesNotExist:
                    pass
            # 如果都找不到，返回404
            return page_404(request)
    except Exception as e:
        print(f"加载页面 {page_name} 时出错: {str(e)}")
        return page_404(request)

# 专门处理404页面的视图
def page_404(request):
    """
    处理404页面请求
    """
    return render(request, '404.html', locals(), status=404)


# ==================== 新的后台管理框架视图函数 ====================

def admin_framework(request):
    """
    后台管理框架主页面视图
    渲染新的管理框架页面
    """
    try:
        return render(request, 'admin-framework.html', {
            'page_title': '后台管理系统',
            'framework_version': '1.0.0'
        })
    except Exception as e:
        print(f"加载管理框架页面时出错: {str(e)}")
        return HttpResponse(f"框架加载失败: {str(e)}", status=500)


def framework_page_view(request, route_path):
    """
    处理框架子页面的AJAX请求
    根据路由路径返回对应的页面内容

    Args:
        request: Django请求对象
        route_path: 路由路径，如 'dashboard', 'products/list', 'users/groups'

    Returns:
        HttpResponse: 页面HTML内容或错误信息
    """

    # 检查是否为AJAX请求
    if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'error': '此端点仅支持AJAX请求',
            'code': 'INVALID_REQUEST_TYPE'
        }, status=400)

    try:
        # 路由映射表 - 将前端路由映射到实际的模板文件（根据实际文件位置调整）
        route_mapping = {
            # 控制台 - 对应原版 pages/home.html
            'home': 'pages/home.html',

            # 商品列表 - 对应原版 productlist.html（在pages目录下）
            'productlist': 'pages/productlist.html',

            # 分类管理 - 对应原版 category.html（在pages目录下）
            'category': 'pages/category.html',

            # 卡密管理 - 对应原版 cardstock.html（在pages目录下）
            'cardstock': 'pages/cardstock.html',

            # 卡券管理 - 对应原版 coupon.html（在pages目录下）
            'coupon': 'pages/coupon.html',

            # 定价模板 - 对应原版 PriceTemplate.html（在pages目录下）
            'PriceTemplate': 'pages/PriceTemplate.html',

            # 对接中心 - 对应原版 DockingCenter.html（在pages目录下）
            'DockingCenter': 'pages/DockingCenter.html',

            # 订单列表 - 新创建的 orderlist.html（在pages目录下）
            'orderlist': 'pages/orderlist.html',

            # 用户列表 - 新创建的 userlist.html（在根目录下）
            'userlist': 'userlist.html',

            # 会员等级 - 对应原版 usergroup.html（在pages目录下）
            'usergroup': 'pages/usergroup.html',

            # 支付设置 - 对应原版 payment.html（在pages目录下）
            'payment': 'pages/payment.html',

            # 插件市场 - 对应原版 plugin.html（在pages目录下）
            'plugin': 'pages/plugin.html',

            # 404错误页面 - 对应 pages/404.html
            '404': 'pages/404.html',
        }

        # 获取对应的模板路径
        template_path = route_mapping.get(route_path)

        if not template_path:
            # 如果路由不存在，返回404状态码让前端处理
            return HttpResponse(
                f'页面未找到: 路由 "{route_path}" 不存在',
                status=404
            )

        # 尝试渲染模板
        try:
            # 检查模板是否存在
            get_template(template_path)

            context = {
                'current_route': route_path,
                'page_title': _get_page_title(route_path),
                'is_framework_page': True,
                'framework_mode': True
            }

            # 渲染并返回页面内容
            return render(request, template_path, context)

        except TemplateDoesNotExist:
            # 如果模板不存在，尝试在pages目录下查找
            try:
                pages_template_path = f'pages/{template_path}'
                get_template(pages_template_path)

                context = {
                    'current_route': route_path,
                    'page_title': _get_page_title(route_path),
                    'is_framework_page': True,
                    'framework_mode': True
                }

                return render(request, pages_template_path, context)

            except TemplateDoesNotExist:
                # 如果都找不到，返回404状态码让前端处理
                return HttpResponse(
                    f'模板未找到: {template_path}',
                    status=404
                )

    except Exception as e:
        print(f"处理框架页面请求时出错 - 路由: {route_path}, 错误: {str(e)}")

        # 返回错误页面
        return HttpResponse(
            f'''
            <div class="af-error-page" style="padding: 40px; text-align: center; color: #666;">
                <h2 style="color: #e74c3c;">页面加载失败</h2>
                <p>路由: <code>{route_path}</code></p>
                <p>错误信息: {str(e)}</p>
                <button onclick="AdminFramework.navigateTo('home')"
                        style="padding: 10px 20px; background: #ff6b9d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    返回控制台
                </button>
            </div>
            ''',
            status=500
        )


def _get_page_title(route_path):
    """
    根据路由路径获取页面标题（与原版index.html完全一致）

    Args:
        route_path: 路由路径

    Returns:
        str: 页面标题
    """
    title_mapping = {
        'home': '控制台',
        'productlist': '商品列表',
        'category': '分类管理',
        'cardstock': '卡密管理',
        'coupon': '卡券管理',
        'PriceTemplate': '定价模板',
        'DockingCenter': '对接中心',
        'orderlist': '订单列表',
        'userlist': '用户列表',
        'usergroup': '会员等级',
        'payment': '支付设置',
        'plugin': '插件市场',
        '404': '页面未找到',
    }

    return title_mapping.get(route_path, '后台管理')


# ==================== Vue混合渲染测试视图 ====================

def vue_test_page(request):
    """
    Vue混合渲染测试页面
    演示如何使用Vue模板替代HTML模板
    """
    try:
        # 准备测试数据
        context = {
            'page_title': 'Vue混合渲染测试',
            'test_data': {
                'message': '这是从Django传递给Vue的数据',
                'items': ['项目1', '项目2', '项目3'],
                'config': {
                    'theme': 'light',
                    'language': 'zh-CN'
                }
            },
            'server_time': timezone.now().isoformat(),
        }
        
        # 使用智能渲染器，自动检测模板类型
        return smart_render(request, 'pages/test-vue.vue', context)
        
    except Exception as e:
        print(f"Vue测试页面加载失败: {str(e)}")
        return HttpResponse(f"Vue页面加载失败: {str(e)}", status=500)


def smart_page_view(request, page_name):
    """
    智能页面视图 - 支持HTML和Vue模板的混合渲染
    这是对原有page_view函数的增强版本
    """
    # 检查请求URL，确定正确的模板路径
    if request.path.startswith('/pages/'):
        template_path = f'pages/{page_name}'
    else:
        template_path = page_name
    
    try:
        # 准备上下文数据
        context = {
            'page_name': page_name,
            'request_path': request.path,
            'current_time': timezone.now(),
        }
        
        # 首先尝试Vue模板
        vue_template_path = f'vue/{template_path}.vue'
        try:
            return smart_render(request, vue_template_path, context)
        except TemplateDoesNotExist:
            pass
        
        # 然后尝试HTML模板
        html_template_path = f'{template_path}.html'
        try:
            return smart_render(request, html_template_path, context)
        except TemplateDoesNotExist:
            pass
        
        # 如果都找不到，尝试在pages目录下查找
        if not template_path.startswith('pages/'):
            try:
                return smart_render(request, f'pages/{page_name}.html', context)
            except TemplateDoesNotExist:
                pass
        
        # 如果都找不到，返回404
        return page_404(request)
        
    except Exception as e:
        print(f"智能页面加载失败 {page_name}: {str(e)}")
        return page_404(request)

