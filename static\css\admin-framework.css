/* 防止页面整体滚动 */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        /* 页面加载动画 */
        .af-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
            transition: all 0.3s ease;
        }
        
        .af-loading-overlay.af-hidden {
            opacity: 0;
            visibility: hidden;
        }
        
        .af-loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #ff6b9d;
            border-radius: 50%;
            animation: af-spin 1s linear infinite;
        }
        
        @keyframes af-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 进度条样式 */
        .af-progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b9d, #ff8fab);
            z-index: 10000;
            transition: width 0.3s ease;
            width: 0%;
        }