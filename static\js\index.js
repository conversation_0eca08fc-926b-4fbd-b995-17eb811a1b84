// 检查登录状态
        function checkLoginStatus() {
            // 检查sessionStorage中是否有登录信息
            const loginUser = sessionStorage.getItem('loginUser');
            const sessionUserId = sessionStorage.getItem('userId');

            // 如果没有登录信息，重定向到登录页面
            if (!loginUser || !sessionUserId) {
                window.location.href = 'logon.html';
                return;
            }
        }

(function () {
            // 会话超时时间（毫秒）：10分钟
            const SESSION_TIMEOUT = 10 * 60 * 1000;
            let sessionTimer;
            let lastActivity = Date.now();

            // 初始化会话管理
            function initSessionManager() {
                // 检查当前登录状态
                checkLoginStatus();

                // 重置会话计时器
                resetSessionTimer();

                // 监听用户活动事件
                const events = ['click', 'touchstart', 'mousemove', 'keydown', 'scroll'];
                events.forEach(event => {
                    document.addEventListener(event, handleUserActivity);
                });
            }

            // 处理用户活动
            function handleUserActivity() {
                lastActivity = Date.now();
                resetSessionTimer();

                // 更新localStorage中的最后活动时间
                localStorage.setItem('lastActivityTime', lastActivity.toString());
            }

            // 重置会话计时器
            function resetSessionTimer() {
                // 清除现有的计时器
                if (sessionTimer) {
                    clearTimeout(sessionTimer);
                }

                // 设置新的计时器
                sessionTimer = setTimeout(() => {
                    handleSessionTimeout();
                }, SESSION_TIMEOUT);
            }

            // 全局认证状态管理
            const AuthStateManager = {
                // 检查是否有Token过期状态
                isTokenExpired: function() {
                    return localStorage.getItem('tokenExpired') === 'true';
                },

                // 检查是否有会话过期状态
                isSessionExpired: function() {
                    return localStorage.getItem('sessionExpired') === 'true';
                },

                // 清除认证状态
                clearAuthStates: function() {
                    localStorage.removeItem('tokenExpired');
                    localStorage.removeItem('sessionExpired');
                },

                // 显示认证模态框
                showAuthModal: function(title, message, buttonText = '重新登录') {
                    const modal = document.getElementById('authModal');
                    const titleElement = document.getElementById('authModalTitle');
                    const messageElement = document.getElementById('authModalMessage');
                    const buttonElement = document.getElementById('authModalButton');

                    if (modal && titleElement && messageElement && buttonElement) {
                        titleElement.textContent = title;
                        messageElement.textContent = message;
                        buttonElement.textContent = buttonText;
                        modal.classList.add('show');

                        // 绑定按钮点击事件
                        buttonElement.onclick = () => {
                            window.location.href = '/admin/backend/logon.html';
                        };
                    }
                }
            };

            // 处理会话超时
            function handleSessionTimeout() {
                // 检查Token过期优先级：如果Token已过期，则不显示会话超时提示
                if (AuthStateManager.isTokenExpired()) {
                    return; // Token过期优先级更高，不显示会话超时
                }

                // 显示会话超时模态框
                AuthStateManager.showAuthModal(
                    '会话已超时',
                    '由于长时间未操作，您的会话已超时。请重新登录以继续。',
                    '重新登录'
                );

                // 标记会话已过期
                localStorage.setItem('sessionExpired', 'true');
            }

            // 检查会话时间是否超时
            function checkSessionTimeout() {
                // 检查是否刚刚登录（从登录页面跳转过来）
                const referrer = document.referrer;
                const isFromLogin = referrer.includes('logon.html');

                // 如果是从登录页面跳转过来，则视为有效会话，清除所有认证状态并更新最后活动时间
                if (isFromLogin) {
                    AuthStateManager.clearAuthStates();
                    localStorage.setItem('lastActivityTime', Date.now().toString());
                    return;
                }

                // 优先级检查：Token过期 > 会话超时
                if (AuthStateManager.isTokenExpired()) {
                    // Token已过期，不需要检查会话超时
                    return;
                }

                // 检查localStorage中的会话状态
                const sessionExpired = localStorage.getItem('sessionExpired') === 'true';
                const lastActivityTime = localStorage.getItem('lastActivityTime');

                if (sessionExpired) {
                    // 如果会话已标记为过期，显示会话超时模态框
                    handleSessionTimeout();
                } else if (lastActivityTime) {
                    // 检查距离上次活动是否已超过超时时间
                    const timeSinceLastActivity = Date.now() - parseInt(lastActivityTime, 10);
                    if (timeSinceLastActivity >= SESSION_TIMEOUT) {
                        handleSessionTimeout();
                    }
                }
            }

            // 重新登录
            document.getElementById('reloginButton').addEventListener('click', function () {
                // 清除会话数据
                localStorage.removeItem('sessionExpired');
                localStorage.removeItem('lastActivityTime');

                // 重定向到登录页面
                window.location.href = 'logon.html';
            });

            // 当页面加载完成后初始化会话管理
            document.addEventListener('DOMContentLoaded', function () {
                // 初始化会话超时检查
                checkSessionTimeout();

                // 重置会话计时器
                resetSessionTimer();

                // 监听用户活动事件
                const events = ['click', 'touchstart', 'mousemove', 'keydown', 'scroll'];
                events.forEach(event => {
                    document.addEventListener(event, handleUserActivity);
                });

                // 初始化菜单功能
                const menuToggle = document.getElementById('menu-toggle');
                if (menuToggle) {
                    menuToggle.addEventListener('click', function () {
                        document.body.classList.toggle('sidebar-collapsed');
                    });
                }
            });
        })();