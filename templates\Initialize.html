<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统初始化向导</title>
    
    <link rel="stylesheet" href="/static/css/Initialize.css">
</head>

<body>
    <div class="container">
        <h1>系统初始化向导</h1>

        <div class="step-indicator">
            <div class="step active">1</div>
            <div class="step">2</div>
            <div class="step">3</div>
        </div>

        <div class="form-step active" style="display: block;">
            <div class="form-grid">
                <div class="form-group">
                    <label>数据库地址</label>
                    <input type="text" id="dbHost" placeholder="localhost" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>数据库名称</label>
                    <input type="text" id="dbName" placeholder="my_database" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>数据库用户</label>
                    <input type="text" id="dbUser" placeholder="root" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>数据库密码</label>
                    <input type="password" id="dbPass" placeholder="••••••••" required>
                    <span class="error-message"></span>
                </div>
            </div>

            <div class="form-group" style="grid-column: 1 / span 2; margin-top: 15px;">
                <label>数据库编码</label>
                <div class="encode-options">
                    <label class="encode-option">
                        <input type="radio" name="dbEncode" value="utf8mb4" checked>
                        <span class="radio-custom"></span>
                        <span class="encode-name">utf8mb4</span>
                        <span class="encode-desc">支持完整的Unicode字符集(推荐)</span>
                    </label>
                    <label class="encode-option">
                        <input type="radio" name="dbEncode" value="utf8">
                        <span class="radio-custom"></span>
                        <span class="encode-name">utf8</span>
                        <span class="encode-desc">基础的Unicode支持</span>
                    </label>
                    <label class="encode-option">
                        <input type="radio" name="dbEncode" value="gbk">
                        <span class="radio-custom"></span>
                        <span class="encode-name">gbk</span>
                        <span class="encode-desc">中文字符集</span>
                    </label>
                    <label class="encode-option">
                        <input type="radio" name="dbEncode" value="big5">
                        <span class="radio-custom"></span>
                        <span class="encode-name">big5</span>
                        <span class="encode-desc">繁体中文字符集</span>
                    </label>
                </div>
            </div>
        </div>

        <div class="form-step" style="display: none;">
            <div class="form-grid">
                <div class="form-group">
                    <label>管理员账号</label>
                    <input type="text" id="adminAccount" placeholder="admin" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>登录密码</label>
                    <input type="password" id="adminPass" placeholder="••••••••" required minlength="8">
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>确认密码</label>
                    <input type="password" id="confirmPass" placeholder="••••••••" required>
                    <span class="error-message"></span>
                </div>
            </div>
        </div>

        <div class="form-step" style="display: none;">
            <div class="summary-box">
                <h3>配置概要</h3>
                <ul class="config-list">
                    <li>数据库地址：<span id="previewHost">-</span></li>
                    <li>数据库名称：<span id="previewName">-</span></li>
                    <li>数据库编码：<span id="previewEncode">-</span></li>
                    <li>管理员账号：<span id="previewAccount">-</span></li>
                </ul>
                <div class="tips">
                    <p>请仔细核对配置信息，点击完成将开始初始化</p>
                </div>
            </div>
        </div>

        <div class="button-group">
            <button class="btn-secondary" disabled>上一步</button>
            <button class="btn-primary">下一步</button>
        </div>
    </div>

    <div class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="success-modal">
        <div class="success-modal-content">
            <div class="success-icon">✓</div>
            <h3 style="color: #333; margin-bottom: 1rem;">初始化成功！</h3>
            <p style="color: #666; line-height: 1.6;">系统已准备就绪<br>即将跳转到控制面板...</p>
            <button class="btn-primary" style="margin-top: 1.5rem;" onclick="closeSuccessModal()">立即进入</button>
        </div>
    </div>

    <div class="custom-modal" id="customModal">
        <div class="custom-modal-content">
            <h3 class="modal-title" id="modalTitle">标题</h3>
            <p class="modal-message" id="modalMessage">消息内容</p>
            <div class="modal-buttons" id="modalButtons">
                <button id="modalCancelBtn" class="modal-btn modal-btn-secondary">取消</button>
                <button id="modalConfirmBtn" class="modal-btn modal-btn-primary">确认</button>
            </div>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <!-- <script src="config.js"></script> -->
    <!-- 引入加密通信模块 -->
    <script src="{% static 'sign.js' %}"></script>

    
    <script src="/static/js/Initialize.js"></script>
</body>

</html>