// 显示初始化状态
        function showInitStatus(message, autoHide = true, isError = false) {
            const statusEl = document.getElementById('initStatus');
            statusEl.textContent = message;
            statusEl.classList.add('show');

            // 如果是错误消息，添加错误样式
            if (isError) {
                statusEl.style.backgroundColor = 'rgba(255, 220, 230, 0.95)';
                statusEl.style.color = '#d32f2f';
                statusEl.style.borderColor = '#ff86a0';
            } else {
                statusEl.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
                statusEl.style.color = '#333';
                statusEl.style.borderColor = 'var(--border-color)';
            }

            if (autoHide) {
                setTimeout(() => {
                    statusEl.classList.remove('show');
                }, 5000); // 5秒后自动隐藏
            }
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const errorEl = document.getElementById('errorMsg');
            errorEl.textContent = message;
            errorEl.classList.add('error-show');

            setTimeout(() => {
                errorEl.classList.remove('error-show');
            }, 5000);
        }

        // 更新按钮状态
        function updateButtonState(isLoading) {
            const btn = document.querySelector('.login-btn');
            const btnText = document.getElementById('btnText');

            if (isLoading) {
                btn.disabled = true;
                btnText.innerHTML = '<span class="loading"></span>处理中...';
                btn.style.opacity = '0.8';
            } else {
                btn.disabled = false;
                btnText.textContent = '进入控制台';
                btn.style.opacity = '1';
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function () {
            updateButtonState(false);
            showInitStatus('系统已就绪', true);
            
            // 添加输入框动画效果
            const inputs = document.querySelectorAll('.input-field');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentNode.style.transform = 'translateY(-3px)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentNode.style.transform = 'translateY(0)';
                });
            });
        });

        // 处理登录按钮点击事件
        async function handleLogin() {
            try {
                // 获取用户名和密码
                const username = document.querySelector('#username').value.trim();
                const password = document.querySelector('#password').value.trim();

                // 基本验证
                if (!username || !password) {
                    showErrorMessage('请输入用户名和密码');
                    return;
                }

                // 显示正在处理的状态
                updateButtonState(true);
                showInitStatus('验证登录信息...');

                // 计算MD5签名
                const combinedString = username + password;
                const sign = md5(combinedString);
                const loginData = {
                    account: username,
                    password: password,
                    sign: sign,
                    timestamp: Date.now()
                };

                // 发送登录请求
                const response = await fetch('/api/loginAdmin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                // 解析响应
                const result = await response.json();

                updateButtonState(false);

                if (result.code == 200) {
                    // 登录成功，处理JWT token
                    showInitStatus('登录成功，正在跳转...', true);

                    // 使用AdminAuth处理token信息
                    const tokenSaved = window.AdminAuth.handleLoginResponse(response);

                    if (tokenSaved) {

                        // 设置一个标志，表示刚刚登录成功，避免立即的认证检查
                        window.AdminAuth.setJustLoggedIn(true);
                    }

                    // 保存登录时间戳到localStorage，用于会话管理
                    localStorage.setItem('lastActivityTime', Date.now().toString());
                    localStorage.setItem('loginTimestamp', Date.now().toString());
                    localStorage.removeItem('sessionExpired');

                    // 为登录按钮添加成功动画
                    const loginBtn = document.querySelector('.login-btn');
                    loginBtn.textContent = '登录成功';
                    loginBtn.style.backgroundColor = 'var(--success-color)';
                    loginBtn.style.backgroundImage = 'linear-gradient(135deg, #ff5ca8, #ff3b8a)';

                    // 跳转到主界面
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1200);
                } else {
                    // 登录失败
                    showInitStatus('登录失败', true, true);
                    showErrorMessage(result.message || '用户名或密码不正确');
                }
            } catch (error) {
                showInitStatus('登录失败', true, true);
                showErrorMessage(error.message || '网络错误，请稍后重试');
                updateButtonState(false);
                
                // 显示重试按钮
                document.getElementById('retry-btn').style.display = 'block';
            }
        }

        // 为输入框添加回车键事件
        document.addEventListener('DOMContentLoaded', function () {
            document.getElementById('username').addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    document.getElementById('password').focus();
                }
            });

            document.getElementById('password').addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    handleLogin();
                }
            });
        });

        // MD5 哈希函数实现
        function md5(string) {
            function cmn(q, a, b, x, s, t) {
                a = add32(add32(a, q), add32(x, t));
                return add32((a << s) | (a >>> (32 - s)), b);
            }

            function ff(a, b, c, d, x, s, t) {
                return cmn((b & c) | ((~b) & d), a, b, x, s, t);
            }

            function gg(a, b, c, d, x, s, t) {
                return cmn((b & d) | (c & (~d)), a, b, x, s, t);
            }

            function hh(a, b, c, d, x, s, t) {
                return cmn(b ^ c ^ d, a, b, x, s, t);
            }

            function ii(a, b, c, d, x, s, t) {
                return cmn(c ^ (b | (~d)), a, b, x, s, t);
            }

            function md5cycle(x, k) {
                let a = x[0], b = x[1], c = x[2], d = x[3];

                a = ff(a, b, c, d, k[0], 7, -680876936);
                d = ff(d, a, b, c, k[1], 12, -389564586);
                c = ff(c, d, a, b, k[2], 17, 606105819);
                b = ff(b, c, d, a, k[3], 22, -1044525330);
                a = ff(a, b, c, d, k[4], 7, -176418897);
                d = ff(d, a, b, c, k[5], 12, 1200080426);
                c = ff(c, d, a, b, k[6], 17, -1473231341);
                b = ff(b, c, d, a, k[7], 22, -45705983);
                a = ff(a, b, c, d, k[8], 7, 1770035416);
                d = ff(d, a, b, c, k[9], 12, -1958414417);
                c = ff(c, d, a, b, k[10], 17, -42063);
                b = ff(b, c, d, a, k[11], 22, -1990404162);
                a = ff(a, b, c, d, k[12], 7, 1804603682);
                d = ff(d, a, b, c, k[13], 12, -40341101);
                c = ff(c, d, a, b, k[14], 17, -1502002290);
                b = ff(b, c, d, a, k[15], 22, 1236535329);

                a = gg(a, b, c, d, k[1], 5, -165796510);
                d = gg(d, a, b, c, k[6], 9, -1069501632);
                c = gg(c, d, a, b, k[11], 14, 643717713);
                b = gg(b, c, d, a, k[0], 20, -373897302);
                a = gg(a, b, c, d, k[5], 5, -701558691);
                d = gg(d, a, b, c, k[10], 9, 38016083);
                c = gg(c, d, a, b, k[15], 14, -660478335);
                b = gg(b, c, d, a, k[4], 20, -405537848);
                a = gg(a, b, c, d, k[9], 5, 568446438);
                d = gg(d, a, b, c, k[14], 9, -1019803690);
                c = gg(c, d, a, b, k[3], 14, -187363961);
                b = gg(b, c, d, a, k[8], 20, 1163531501);
                a = gg(a, b, c, d, k[13], 5, -1444681467);
                d = gg(d, a, b, c, k[2], 9, -51403784);
                c = gg(c, d, a, b, k[7], 14, 1735328473);
                b = gg(b, c, d, a, k[12], 20, -1926607734);

                a = hh(a, b, c, d, k[5], 4, -378558);
                d = hh(d, a, b, c, k[8], 11, -2022574463);
                c = hh(c, d, a, b, k[11], 16, 1839030562);
                b = hh(b, c, d, a, k[14], 23, -35309556);
                a = hh(a, b, c, d, k[1], 4, -1530992060);
                d = hh(d, a, b, c, k[4], 11, 1272893353);
                c = hh(c, d, a, b, k[7], 16, -155497632);
                b = hh(b, c, d, a, k[10], 23, -1094730640);
                a = hh(a, b, c, d, k[13], 4, 681279174);
                d = hh(d, a, b, c, k[0], 11, -358537222);
                c = hh(c, d, a, b, k[3], 16, -722521979);
                b = hh(b, c, d, a, k[6], 23, 76029189);
                a = hh(a, b, c, d, k[9], 4, -640364487);
                d = hh(d, a, b, c, k[12], 11, -421815835);
                c = hh(c, d, a, b, k[15], 16, 530742520);
                b = hh(b, c, d, a, k[2], 23, -995338651);

                a = ii(a, b, c, d, k[0], 6, -198630844);
                d = ii(d, a, b, c, k[7], 10, 1126891415);
                c = ii(c, d, a, b, k[14], 15, -1416354905);
                b = ii(b, c, d, a, k[5], 21, -57434055);
                a = ii(a, b, c, d, k[12], 6, 1700485571);
                d = ii(d, a, b, c, k[3], 10, -1894986606);
                c = ii(c, d, a, b, k[10], 15, -1051523);
                b = ii(b, c, d, a, k[1], 21, -2054922799);
                a = ii(a, b, c, d, k[8], 6, 1873313359);
                d = ii(d, a, b, c, k[15], 10, -30611744);
                c = ii(c, d, a, b, k[6], 15, -1560198380);
                b = ii(b, c, d, a, k[13], 21, 1309151649);
                a = ii(a, b, c, d, k[4], 6, -145523070);
                d = ii(d, a, b, c, k[11], 10, -1120210379);
                c = ii(c, d, a, b, k[2], 15, 718787259);
                b = ii(b, c, d, a, k[9], 21, -343485551);

                x[0] = add32(a, x[0]);
                x[1] = add32(b, x[1]);
                x[2] = add32(c, x[2]);
                x[3] = add32(d, x[3]);
            }

            function md5blk(s) {
                let i, md5blks = [];
                for (i = 0; i < 64; i += 4) {
                    md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
                }
                return md5blks;
            }

            function md5blk_array(a) {
                let i, md5blks = [];
                for (i = 0; i < 64; i += 4) {
                    md5blks[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);
                }
                return md5blks;
            }

            function md51(s) {
                let n = s.length,
                    state = [1732584193, -271733879, -1732584194, 271733878],
                    i,
                    length,
                    tail,
                    tmp,
                    lo,
                    hi;

                for (i = 64; i <= n; i += 64) {
                    md5cycle(state, md5blk(s.substring(i - 64, i)));
                }
                s = s.substring(i - 64);
                length = s.length;
                tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
                for (i = 0; i < length; i++) {
                    tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);
                }
                tail[i >> 2] |= 0x80 << ((i % 4) << 3);
                if (i > 55) {
                    md5cycle(state, tail);
                    for (i = 0; i < 16; i++) tail[i] = 0;
                }

                // Beware that the final length might not fit in 32 bits so we take care of that
                tmp = n * 8;
                tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
                lo = parseInt(tmp[2], 16);
                hi = parseInt(tmp[1], 16) || 0;

                tail[14] = lo;
                tail[15] = hi;
                md5cycle(state, tail);
                return state;
            }

            function rhex(n) {
                let s = '', j;
                for (j = 0; j < 4; j++)
                    s += hex_chr[(n >> (j * 8 + 4)) & 0x0F] + hex_chr[(n >> (j * 8)) & 0x0F];
                return s;
            }

            function hex(x) {
                for (let i = 0; i < x.length; i++)
                    x[i] = rhex(x[i]);
                return x.join('');
            }

            // Convert a string to a sequence of UTF-8 bytes
            function str2rstr_utf8(input) {
                let output = "";
                let i = -1;
                let x, y;

                while (++i < input.length) {
                    // Decode utf-16 surrogate pairs
                    x = input.charCodeAt(i);
                    y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;
                    if (0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF) {
                        x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);
                        i++;
                    }

                    // Encode output as utf-8
                    if (x <= 0x7F)
                        output += String.fromCharCode(x);
                    else if (x <= 0x7FF)
                        output += String.fromCharCode(0xC0 | ((x >>> 6) & 0x1F),
                            0x80 | (x & 0x3F));
                    else if (x <= 0xFFFF)
                        output += String.fromCharCode(0xE0 | ((x >>> 12) & 0x0F),
                            0x80 | ((x >>> 6) & 0x3F),
                            0x80 | (x & 0x3F));
                    else if (x <= 0x1FFFFF)
                        output += String.fromCharCode(0xF0 | ((x >>> 18) & 0x07),
                            0x80 | ((x >>> 12) & 0x3F),
                            0x80 | ((x >>> 6) & 0x3F),
                            0x80 | (x & 0x3F));
                }
                return output;
            }

            // Add integers, wrapping at 2^32
            function add32(a, b) {
                return (a + b) & 0xFFFFFFFF;
            }

            const hex_chr = '0123456789abcdef'.split('');

            if (/[\u0080-\uFFFF]/.test(string)) {
                string = str2rstr_utf8(string);
            }

            return hex(md51(string));
        }