{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无名SUP - 后台管理系统</title>
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="preload" href="{% static 'css/style.css' %}" as="style">
    <link rel="preload" href="{% static 'js/main.js' %}" as="script">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <link rel="stylesheet" href="/static/css/index.css">
</head>

<body>
    <!-- 进度条 -->
    <div id="nprogress" class="nprogress-bar" style="width:0%"></div>

    <!-- 顶部白色区块 -->
    <header class="top-bar">
        <div class="left-section">
            <button id="menu-toggle" class="menu-toggle" aria-label="切换导航菜单" data-sidebar-toggle="true">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <i class="fas fa-store"></i>
                <span>无名SUP</span>
            </div>
        </div>
        <div class="user-info">
            <div class="notification">
                <i class="far fa-bell"></i>
                <span class="notification-badge">3</span>
            </div>
            <div class="user-avatar">管</div>
        </div>
    </header>

    <!-- 侧边导航栏 -->
    <nav class="sidebar" aria-label="主导航">
        <ul class="nav-list">
            <li class="nav-item active" data-page="pages/home.html">
                <a href="#"><i class="fas fa-home"></i> 控制台</a>
            </li>
            <li class="nav-item" data-page="productlist.html">
                <a href="#"><i class="fas fa-boxes"></i> 商品列表</a>
            </li>
            <li class="nav-item" data-page="category.html">
                <a href="#"><i class="fas fa-folder"></i> 分类管理</a>
            </li>
            <li class="nav-item" data-page="cardstock.html">
                <a href="#"><i class="fas fa-key"></i> 卡密管理</a>
            </li>
            <li class="nav-item" data-page="coupon.html">
                <a href="#"><i class="fas fa-ticket-alt"></i> 卡券管理</a>
            </li>
            <li class="nav-item" data-page="PriceTemplate.html">
                <a href="#"><i class="fas fa-tags"></i> 定价模板</a>
            </li>
            <li class="nav-item" data-page="DockingCenter.html">
                <a href="#"><i class="fas fa-handshake"></i> 对接中心</a>
            </li>
            <li class="nav-item" data-page="orderlist.html">
                <a href="#"><i class="fas fa-clipboard-list"></i> 订单列表</a>
            </li>
            <li class="nav-item" data-page="userlist.html">
                <a href="#"><i class="fas fa-user"></i> 用户列表</a>
            </li>
            <li class="nav-item" data-page="usergroup.html">
                <a href="#"><i class="fas fa-user-tag"></i> 会员等级</a>
            </li>
            <li class="nav-item" data-page="payment.html">
                <a href="#"><i class="fas fa-credit-card"></i> 支付设置</a>
            </li>
            <li class="nav-item" data-page="plugin.html">
                <a href="#"><i class="fas fa-puzzle-piece"></i> 插件市场</a>
            </li>
        </ul>
    </nav>

    <!-- 内容区域 -->
    <main class="content">
        <div id="page-content"></div>
    </main>

    <!-- 认证提示模态框 (支持Token过期和会话超时) -->
    <div id="authModal" class="session-timeout-modal">
        <div class="session-timeout-content">
            <h3 id="authModalTitle" class="session-timeout-title">认证提示</h3>
            <p id="authModalMessage" class="session-timeout-message">请重新登录以继续。</p>
            <button id="authModalButton" class="session-timeout-button">重新登录</button>
        </div>
    </div>

    
    <!-- 管理员认证工具 -->
    <script src="{% static 'js/admin-auth.js' %}"></script>
    <script src="{% static 'js/main.js' %}"></script>
    <!-- 会话超时管理脚本 -->
    
    <script src="/static/js/index.js"></script>
</body>

</html>